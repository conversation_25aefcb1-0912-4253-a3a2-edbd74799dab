using Avalonia;
using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using HeroYulgang.Views;
using HeroYulgang.Core;
using HeroYulgang.Services;
using RxjhServer;
using Akka.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using HeroYulgang.Core.Actors;
using Akka.Actor;

namespace HeroYulgang;

class Program
{
    [STAThread]
    public static void Main(string[] args)
    {
        // Register CodePagesEncodingProvider to support Windows-1252 encoding
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

        // Check for headless mode
        bool headlessMode = Array.Exists(args, arg => arg.Equals("--headless", StringComparison.OrdinalIgnoreCase));
        bool autoStart = Array.Exists(args, arg => arg.Equals("--autostart", StringComparison.OrdinalIgnoreCase) || arg.Equals("--auto-start", StringComparison.OrdinalIgnoreCase));

        // Check for custom config path
        string configPath = null;
        string appSettingsPath = null;
        for (int i = 0; i < args.Length; i++)
        {
            if (args[i].StartsWith("--config=", StringComparison.OrdinalIgnoreCase))
            {
                configPath = args[i].Substring("--config=".Length);
            }
            else if (args[i].StartsWith("--appsettings=", StringComparison.OrdinalIgnoreCase))
            {
                appSettingsPath = args[i].Substring("--appsettings=".Length);
            }
        }

        if (headlessMode)
        {
            Console.WriteLine($"DEBUG: headlessMode={headlessMode}, autoStart={autoStart}");

            var host = Host.CreateDefaultBuilder(args)
                .ConfigureLogging(logging =>
                {
                    logging.ClearProviders();
                    logging.AddConsole();
                    logging.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Debug);
                })
                .ConfigureServices((context, services) =>
                {
                    // Cấu hình Akka.Hosting với AOT support
                    services.AddAkka("HeroYulgangSystem", (builder, provider) =>
                    {
                        // Cấu hình cơ bản cho AOT - không cần WithActors vì sẽ đăng ký trong ActorSystemManager
                    });

                    // Đăng ký các service khác
                    services.AddSingleton<World>(provider =>
                    {
                        var actorSystem = provider.GetRequiredService<ActorSystem>();
                        var actorRegistry = provider.GetRequiredService<ActorRegistry>();
                        var actorSystemManager = new ActorSystemManager(actorSystem, actorRegistry);
                        return new World(actorSystemManager);
                    });
                    services.AddSingleton<LoginServerClient>(_ => LoginServerClient.Instance);
                })
                .Build();

            // Lấy World từ DI và chạy logic headless (World đã có ActorSystemManager)
            var world = host.Services.GetRequiredService<World>();

            RunHeadlessDI(world, autoStart, configPath, appSettingsPath).Wait();
        }
        else
        {
            // Normal Avalonia GUI mode
            BuildAvaloniaApp()
                .StartWithClassicDesktopLifetime(args);
        }
    }

    private static async Task RunHeadlessDI(World world, bool autoStart, string customConfigPath, string customAppSettingsPath = null)
    {
        try
        {
            Console.WriteLine("=== HeroYulgang GameServer (Headless Mode) ===");

            // Load custom appsettings if specified, or auto-detect from config path
            if (!string.IsNullOrEmpty(customAppSettingsPath))
            {
                Console.WriteLine($"Loading custom appsettings: {customAppSettingsPath}");
                ConfigManager.Instance.LoadCustomAppSettings(customAppSettingsPath);
            }
            else if (!string.IsNullOrEmpty(customConfigPath))
            {
                // Auto-detect appsettings.json in the same directory as config.json
                string configDirectory = Path.GetDirectoryName(customConfigPath);
                if (!string.IsNullOrEmpty(configDirectory))
                {
                    string autoAppSettingsPath = Path.Combine(configDirectory, "appsettings.json");
                    if (File.Exists(autoAppSettingsPath))
                    {
                        Console.WriteLine($"Auto-loading appsettings from config directory: {autoAppSettingsPath}");
                        ConfigManager.Instance.LoadCustomAppSettings(autoAppSettingsPath);
                    }
                }
            }

            // Load custom config if specified
            if (!string.IsNullOrEmpty(customConfigPath))
            {
                Console.WriteLine($"Loading custom config: {customConfigPath}");
                ConfigManager.Instance.LoadCustomConfig(customConfigPath);
            }

            // Initialize logger
            var logger = Logger.Instance;
            logger.Info("GameServer starting in headless mode...");

            // Initialize configuration
            var config = ConfigManager.Instance;
            logger.Info($"Server: {config.ServerSettings.ServerName}");
            logger.Info($"Port: {config.ServerSettings.GameServerPort}");

            // Initialize World
            logger.Info("Initializing World...");

            if (autoStart)
            {
                // Auto-start the world
                logger.Info("Auto-starting World...");

                // Đợi một chút để đảm bảo ActorSystem đã được khởi tạo hoàn toàn
                await Task.Delay(1000);

                bool success = await world.StartAsync();

                if (success)
                {
                    logger.Info("✓ GameServer started successfully in headless mode");
                    logger.Info($"✓ Server is listening on port {config.ServerSettings.GameServerPort}");
                    logger.Info("✓ Ready to accept player connections");

                    // Keep the application running without blocking console input
                    Console.WriteLine("GameServer is running. Commands: 'q' to quit, 's' to show status...");
                    await RunHeadlessConsoleNonBlocking(world);
                }
                else
                {
                    logger.Error("✗ Failed to start GameServer");
                    Environment.Exit(1);
                }
            }
            else
            {
                logger.Info("GameServer initialized. Use 'start' command to begin.");
                await RunHeadlessConsoleNonBlocking(world);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Fatal error in headless mode: {ex}");
            Environment.Exit(1);
        }
    }

    private static async Task RunHeadlessConsoleNonBlocking(World world)
    {
        // Set console encoding to UTF-8 for Vietnamese characters
        try
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.InputEncoding = System.Text.Encoding.UTF8;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Could not set console encoding: {ex.Message}");
        }

        var cancellationTokenSource = new System.Threading.CancellationTokenSource();

        // Handle console input in background task - only if console is available
        Task consoleTask = null;

        try
        {
            // Check if console input is available (not redirected)
            if (!Console.IsInputRedirected && Environment.UserInteractive)
            {
                consoleTask = Task.Run(async () =>
                {
                    while (!cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        try
                        {
                            // Use a safer approach without Console.KeyAvailable
                            var inputTask = Task.Run(() => Console.ReadLine());
                            var delayTask = Task.Delay(1000, cancellationTokenSource.Token);

                            var completedTask = await Task.WhenAny(inputTask, delayTask);

                            if (completedTask == inputTask && inputTask.Result != null)
                            {
                                var input = inputTask.Result.ToLower().Trim();
                                await HandleConsoleCommand(input, world, cancellationTokenSource);
                            }
                        }
                        catch (OperationCanceledException)
                        {
                            break;
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Console input error: {ex.Message}");
                            await Task.Delay(5000, cancellationTokenSource.Token);
                        }
                    }
                }, cancellationTokenSource.Token);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Console setup error: {ex.Message}");
        }

        // Keep the main thread alive
        try
        {
            if (consoleTask != null)
            {
                await consoleTask;
            }
            else
            {
                // If no console available, just wait indefinitely
                await Task.Delay(-1, cancellationTokenSource.Token);
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when shutting down
        }
    }

    private static async Task HandleConsoleCommand(string input, World world, System.Threading.CancellationTokenSource cancellationTokenSource)
    {
        switch (input)
        {
            case "q":
            case "quit":
            case "exit":
                Console.WriteLine("Shutting down GameServer...");
                if (world.State == WorldState.Running)
                {
                    await world.StopAsync();
                }
                cancellationTokenSource.Cancel();
                Environment.Exit(0);
                break;

            case "s":
            case "status":
                ShowStatus(world);
                break;

            case "start":
                if (world.State != WorldState.Running)
                {
                    Console.WriteLine("Starting World...");
                    bool success = await world.StartAsync();
                    Console.WriteLine(success ? "✓ World started" : "✗ Failed to start World");
                }
                else
                {
                    Console.WriteLine("World is already running");
                }
                break;

            case "stop":
                if (world.State == WorldState.Running)
                {
                    Console.WriteLine("Stopping World...");
                    bool success = await world.StopAsync();
                    Console.WriteLine(success ? "✓ World stopped" : "✗ Failed to stop World");
                }
                else
                {
                    Console.WriteLine("World is not running");
                }
                break;

            case "restart":
                Console.WriteLine("Restarting World...");
                if (world.State == WorldState.Running)
                {
                    await world.StopAsync();
                    await Task.Delay(2000);
                }
                bool restartSuccess = await world.StartAsync();
                Console.WriteLine(restartSuccess ? "✓ World restarted" : "✗ Failed to restart World");
                break;

            case "help":
            case "h":
                ShowHelp();
                break;

            default:
                if (!string.IsNullOrEmpty(input))
                {
                    Console.WriteLine($"Unknown command: {input}. Type 'help' for available commands.");
                }
                break;
        }
    }

    private static async Task RunHeadlessConsole(World world)
    {
        while (true)
        {
            var input = Console.ReadLine()?.ToLower().Trim();

            switch (input)
            {
                case "q":
                case "quit":
                case "exit":
                    Console.WriteLine("Shutting down GameServer...");
                    if (world.State == WorldState.Running)
                    {
                        await world.StopAsync();
                    }
                    Environment.Exit(0);
                    break;

                case "s":
                case "status":
                    ShowStatus(world);
                    break;

                case "start":
                    if (world.State != WorldState.Running)
                    {
                        Console.WriteLine("Starting World...");
                        bool success = await world.StartAsync();
                        Console.WriteLine(success ? "✓ World started" : "✗ Failed to start World");
                    }
                    else
                    {
                        Console.WriteLine("World is already running");
                    }
                    break;

                case "stop":
                    if (world.State == WorldState.Running)
                    {
                        Console.WriteLine("Stopping World...");
                        bool success = await world.StopAsync();
                        Console.WriteLine(success ? "✓ World stopped" : "✗ Failed to stop World");
                    }
                    else
                    {
                        Console.WriteLine("World is not running");
                    }
                    break;

                case "restart":
                    Console.WriteLine("Restarting World...");
                    if (world.State == WorldState.Running)
                    {
                        await world.StopAsync();
                        await Task.Delay(2000);
                    }
                    bool restartSuccess = await world.StartAsync();
                    Console.WriteLine(restartSuccess ? "✓ World restarted" : "✗ Failed to restart World");
                    break;

                case "help":
                case "h":
                    ShowHelp();
                    break;

                default:
                    if (!string.IsNullOrEmpty(input))
                    {
                        Console.WriteLine($"Unknown command: {input}. Type 'help' for available commands.");
                    }
                    break;
            }
        }
    }

    private static void ShowStatus(World world)
    {
        Console.WriteLine("\n=== GameServer Status ===");
        Console.WriteLine($"State: {world.State}");
        Console.WriteLine($"Uptime: {(world.State == WorldState.Running ? DateTime.Now - world.StartTime : TimeSpan.Zero):hh\\:mm\\:ss}");
        Console.WriteLine($"Port: {ConfigManager.Instance.ServerSettings.GameServerPort}");
        Console.WriteLine($"Max Players: {ConfigManager.Instance.ServerSettings.MaximumOnline}");
        Console.WriteLine($"Server Name: {ConfigManager.Instance.ServerSettings.ServerName}");
        Console.WriteLine("========================\n");
    }

    private static void ShowHelp()
    {
        Console.WriteLine("\n=== Available Commands ===");
        Console.WriteLine("start    - Start the World");
        Console.WriteLine("stop     - Stop the World");
        Console.WriteLine("restart  - Restart the World");
        Console.WriteLine("status   - Show server status");
        Console.WriteLine("quit     - Shutdown server");
        Console.WriteLine("help     - Show this help");
        Console.WriteLine("=========================\n");
    }

    // Avalonia configuration, don't remove; also used by visual designer.
    public static AppBuilder BuildAvaloniaApp()
        => AppBuilder.Configure<App>()
            .UsePlatformDetect()
            .WithInterFont()
            .LogToTrace();
}
