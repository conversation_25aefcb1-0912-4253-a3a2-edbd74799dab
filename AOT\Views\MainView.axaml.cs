using System;
using System.IO;
using System.Threading.Tasks;
using Avalonia.Controls;
using Avalonia.Interactivity;
using HeroYulgang.Services;
using RxjhServer;
using RxjhServer.Database;
using System.Diagnostics;
using System.Threading;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;

namespace HeroYulgang.Views
{
    public partial class MainView : UserControl
    {
        private World? _world;
        private bool _isWorldInitialized = false;
        private readonly IServiceProvider? _serviceProvider;

        public MainView()
        {
            InitializeComponent();
            SetupEventHandlers();
            UpdateButtonStates();

            // Lấy ServiceProvider từ Program.GuiServiceProvider
            if (Program.GuiServiceProvider != null)
            {
                _serviceProvider = Program.GuiServiceProvider;
                // Khởi tạo World async từ DI
                _ = InitializeWorldAsync();
            }
            else
            {
                // Fallback cho trường hợp không có DI (chế độ design)
                Logger.Instance.Warning("ServiceProvider không có sẵn, sử dụng World.Instance");
                _ = InitializeWorldFallbackAsync();
            }
        }

        private async Task InitializeWorldAsync()
        {
            try
            {
                Logger.Instance.Info("Đang khởi tạo World từ DI...");

                // Lấy World từ DI container
                _world = _serviceProvider!.GetRequiredService<World>();

                // Khởi tạo heavy components async
                await _world.InitializeHeavyComponentsAsync();

                _isWorldInitialized = true;
                Logger.Instance.Info("World đã được khởi tạo thành công từ DI");

                // Cập nhật UI trên UI thread
                await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(UpdateButtonStates);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi tạo World từ DI: {ex.Message}");

                await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(UpdateButtonStates);
            }
        }

        private async Task InitializeWorldFallbackAsync()
        {
            try
            {
                Logger.Instance.Info("Đang khởi tạo World (fallback)...");

                // Fallback sử dụng singleton cũ
                _world = World.Instance;

                // Khởi tạo heavy components async
                await _world.InitializeHeavyComponentsAsync();

                _isWorldInitialized = true;
                Logger.Instance.Info("World đã được khởi tạo thành công (fallback)");

                // Cập nhật UI trên UI thread
                await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(UpdateButtonStates);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi tạo World (fallback): {ex.Message}");

                await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(UpdateButtonStates);
            }
        }

        private void SetupEventHandlers()
        {
            StartServerButton.Click += OnStartServerClick;
            StopServerButton.Click += OnStopServerClick;
            RestartServerButton.Click += OnRestartServerClick;
            DbStressTestButton.Click += OnDbStressTestClick;
            TestErrorLogButton.Click += OnTestErrorLogClick;
        }

        private async void OnStartServerClick(object? sender, RoutedEventArgs e)
        {
            if (_world == null)
            {
                Logger.Instance.Warning("World chưa được khởi tạo. Vui lòng đợi...");
                return;
            }

            // Vô hiệu hóa các nút trong khi khởi động
            SetButtonsEnabled(false);

            Logger.Instance.Info("Đang khởi động máy chủ...");

            bool success = await _world.StartAsync();

            if (success)
            {
                Logger.Instance.Info("Máy chủ đã khởi động thành công");
                Logger.Instance.Debug("Đang lắng nghe kết nối từ người chơi");
                Logger.Instance.Info("Máy chủ đã sẵn sàng phục vụ người chơi");
            }
            else
            {
                Logger.Instance.Error("Không thể khởi động máy chủ");
            }

            // Cập nhật trạng thái nút
            UpdateButtonStates();
        }

        private async void OnStopServerClick(object? sender, RoutedEventArgs e)
        {
            if (_world == null)
            {
                Logger.Instance.Warning("World chưa được khởi tạo.");
                return;
            }

            // Vô hiệu hóa các nút trong khi dừng
            SetButtonsEnabled(false);

            Logger.Instance.Info("Đang dừng máy chủ...");

            bool success = await _world.StopAsync();

            if (success)
            {
                Logger.Instance.Info("Máy chủ đã dừng thành công");
                Logger.Instance.Debug("Đã đóng tất cả kết nối");
            }
            else
            {
                Logger.Instance.Error("Không thể dừng máy chủ");
            }

            // Cập nhật trạng thái nút
            UpdateButtonStates();
        }

        private async void OnRestartServerClick(object? sender, RoutedEventArgs e)
        {
            if (_world == null)
            {
                Logger.Instance.Warning("World chưa được khởi tạo.");
                return;
            }

            // Vô hiệu hóa các nút trong khi khởi động lại
            SetButtonsEnabled(false);

            Logger.Instance.Info("Đang khởi động lại máy chủ...");

            await _world.RestartAsync();

            Logger.Instance.Info("Máy chủ đã khởi động lại thành công");
            Logger.Instance.Info("Máy chủ đã sẵn sàng phục vụ người chơi");

            // Cập nhật trạng thái nút
            UpdateButtonStates();
        }

        private void UpdateButtonStates()
        {
            if (_world == null)
            {
                // World chưa được khởi tạo
                StartServerButton.IsEnabled = false;
                StopServerButton.IsEnabled = false;
                RestartServerButton.IsEnabled = false;
                DbStressTestButton.IsEnabled = true; // Có thể test DB ngay cả khi World chưa khởi tạo
                return;
            }

            switch (_world.State)
            {
                case WorldState.Stopped:
                    StartServerButton.IsEnabled = _isWorldInitialized;
                    StopServerButton.IsEnabled = false;
                    RestartServerButton.IsEnabled = false;
                    DbStressTestButton.IsEnabled = true; // Có thể test DB khi server dừng
                    break;

                case WorldState.Running:
                    StartServerButton.IsEnabled = false;
                    StopServerButton.IsEnabled = true;
                    RestartServerButton.IsEnabled = true;
                    DbStressTestButton.IsEnabled = true; // Có thể test DB khi server chạy
                    break;

                case WorldState.Starting:
                case WorldState.Stopping:
                    StartServerButton.IsEnabled = false;
                    StopServerButton.IsEnabled = false;
                    RestartServerButton.IsEnabled = false;
                    DbStressTestButton.IsEnabled = false; // Không test DB khi server đang chuyển trạng thái
                    break;
            }
        }

        private void SetButtonsEnabled(bool enabled)
        {
            StartServerButton.IsEnabled = enabled;
            StopServerButton.IsEnabled = enabled;
            RestartServerButton.IsEnabled = enabled;
        }

        private async void OnDbStressTestClick(object? sender, RoutedEventArgs e)
        {
            try
            {
                // Vô hiệu hóa button trong khi test
                DbStressTestButton.IsEnabled = false;
                DbStressTestButton.Content = "Testing...";

                Logger.Instance.Info("=== BẮT ĐẦU DB STRESS TEST ===");
                Logger.Instance.Info("Mục đích: Test connection pooling và queue khi có nhiều query đồng thời");

                await RunDatabaseStressTest();

                Logger.Instance.Info("=== HOÀN THÀNH DB STRESS TEST ===");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi trong quá trình stress test: {ex.Message}");
            }
            finally
            {
                // Khôi phục button
                DbStressTestButton.IsEnabled = true;
                DbStressTestButton.Content = "DB Stress Test";
            }
        }

        private void OnTestErrorLogClick(object? sender, RoutedEventArgs e)
        {
            try
            {
                Logger.Instance.Info("=== BẮT ĐẦU TEST ERROR LOGGING ===");
                Logger.Instance.Info("Đang tạo các log Error và Fatal để test ghi file theo ngày...");

                // Gọi method test error logging
                Logger.TestErrorLogging();

                Logger.Instance.Info("=== KẾT THÚC TEST ERROR LOGGING ===");
                Logger.Instance.Info($"Kiểm tra file log error tại: {Logger.GetCurrentErrorLogFilePath()}");
                Logger.Instance.Info($"File log error có tồn tại: {Logger.ErrorLogFileExists()}");
                Logger.Instance.Info($"Kích thước file log error: {Logger.GetErrorLogFileSize()} bytes");

                // Hiển thị danh sách tất cả file error log
                var errorLogFiles = Logger.GetAllErrorLogFiles();
                if (errorLogFiles.Count > 0)
                {
                    Logger.Instance.Info($"Tìm thấy {errorLogFiles.Count} file(s) log error:");
                    foreach (var file in errorLogFiles.Take(5)) // Chỉ hiển thị 5 file gần nhất
                    {
                        var fileName = Path.GetFileName(file);
                        var fileSize = new FileInfo(file).Length;
                        Logger.Instance.Info($"  - {fileName} ({fileSize} bytes)");
                    }
                }
                else
                {
                    Logger.Instance.Warning("Không tìm thấy file log error nào");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi test error logging: {ex.Message}");
            }
        }

        private async Task RunDatabaseStressTest()
        {
            // const int numberOfConcurrentQueries = 100; // Số lượng query chạy đồng thời
            // const int totalQueries = 10000; // Tổng số query sẽ chạy

            // var stopwatch = Stopwatch.StartNew();
            // var completedQueries = 0;
            // var failedQueries = 0;
            // var semaphore = new SemaphoreSlim(numberOfConcurrentQueries); // Giới hạn số query đồng thời

            // Logger.Instance.Info($"Cấu hình test: {totalQueries} queries tổng cộng, tối đa {numberOfConcurrentQueries} queries đồng thời");

            // // SQL query tương tự như yêu cầu
            // const string testQuery = @"
            //     SELECT *
            //     FROM TBL_XWWL_Char
            //     WHERE FLD_ID = '1'
            //     ";

            // // Tạo danh sách tasks
            // var tasks = Enumerable.Range(1, totalQueries).Select(async queryId =>
            // {
            //     await semaphore.WaitAsync(); // Chờ slot available
            //     try
            //     {
            //         var queryStopwatch = Stopwatch.StartNew();

            //         // Thực hiện query
            //         var result = await Task.Run(() => DBA.GetDBToDataTable(testQuery, "GameDb"));

            //         queryStopwatch.Stop();

            //         if (result != null && result.Rows.Count > 0)
            //         {
            //             Interlocked.Increment(ref completedQueries);
            //             Logger.Instance.Debug($"Query #{queryId}: Thành công - {result.Rows.Count} rows - {queryStopwatch.ElapsedMilliseconds}ms");
            //         }
            //         else
            //         {
            //             Interlocked.Increment(ref failedQueries);
            //             Logger.Instance.Warning($"Query #{queryId}: Không có dữ liệu - {queryStopwatch.ElapsedMilliseconds}ms");
            //         }
            //     }
            //     catch (Exception ex)
            //     {
            //         Interlocked.Increment(ref failedQueries);
            //         Logger.Instance.Error($"Query #{queryId}: Lỗi - {ex.Message}");
            //     }
            //     finally
            //     {
            //         semaphore.Release(); // Giải phóng slot
            //     }
            // });

            // // Chạy tất cả tasks và chờ hoàn thành
            // await Task.WhenAll(tasks);

            // stopwatch.Stop();

            // // Báo cáo kết quả
            // Logger.Instance.Info($"KẾT QUẢ STRESS TEST:");
            // Logger.Instance.Info($"- Tổng thời gian: {stopwatch.ElapsedMilliseconds}ms ({stopwatch.Elapsed.TotalSeconds:F2}s)");
            // Logger.Instance.Info($"- Queries thành công: {completedQueries}/{totalQueries}");
            // Logger.Instance.Info($"- Queries thất bại: {failedQueries}/{totalQueries}");
            // Logger.Instance.Info($"- Tỷ lệ thành công: {(completedQueries * 100.0 / totalQueries):F1}%");
            // Logger.Instance.Info($"- Thời gian trung bình mỗi query: {(stopwatch.ElapsedMilliseconds / (double)totalQueries):F2}ms");
            // Logger.Instance.Info($"- Throughput: {(totalQueries / stopwatch.Elapsed.TotalSeconds):F2} queries/second");
        }
    }
}
