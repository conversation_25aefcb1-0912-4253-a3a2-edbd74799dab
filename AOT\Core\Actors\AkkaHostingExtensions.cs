using System;
using Akka.Actor;
using Akka.Hosting;
using Microsoft.Extensions.DependencyInjection;

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// Extension methods cho Akka.Hosting để đăng ký các Actor với AOT support
    /// </summary>
    public static class AkkaHostingExtensions
    {
        /// <summary>
        /// Đăng ký TcpManagerActor với Akka.Hosting
        /// </summary>
        public static AkkaConfigurationBuilder WithTcpManagerActor(this AkkaConfigurationBuilder builder)
        {
            return builder.WithActors((system, registry, resolver) =>
            {
                try
                {
                    Console.WriteLine("DEBUG: Bắt đầu tạo TcpManagerActor...");

                    // Tạo TcpManagerActor với dependency injection support
                    var tcpManagerProps = resolver.Props<TcpManagerActor>();
                    Console.WriteLine("DEBUG: Props đã được tạo");

                    var tcpManager = system.ActorOf(tcpManagerProps, "tcpManager");
                    Console.WriteLine($"DEBUG: TcpManagerActor đã được tạo: {tcpManager}");

                    // Đăng ký vào ActorRegistry
                    registry.Register<TcpManagerActor>(tcpManager);
                    Console.WriteLine("DEBUG: TcpManagerActor đã được đăng ký vào registry");

                    Console.WriteLine("DEBUG: TcpManagerActor đã được đăng ký với Akka.Hosting thành công");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"ERROR: Lỗi khi tạo TcpManagerActor: {ex.Message}");
                    Console.WriteLine($"ERROR: StackTrace: {ex.StackTrace}");
                    throw;
                }
            });
        }

        /// <summary>
        /// Đăng ký PacketHandlerActor với Akka.Hosting
        /// </summary>
        public static AkkaConfigurationBuilder WithPacketHandlerActor(this AkkaConfigurationBuilder builder)
        {
            return builder.WithActors((system, registry, resolver) =>
            {
                // PacketHandlerActor sẽ được tạo động khi cần thiết
                // Chỉ đăng ký Props để có thể tạo sau này
                var packetHandlerProps = resolver.Props<PacketHandlerActor>();
                
                Console.WriteLine("DEBUG: PacketHandlerActor Props đã được đăng ký với Akka.Hosting");
            });
        }

        /// <summary>
        /// Đăng ký ClientActor với Akka.Hosting
        /// </summary>
        public static AkkaConfigurationBuilder WithClientActor(this AkkaConfigurationBuilder builder)
        {
            return builder.WithActors((system, registry, resolver) =>
            {
                // ClientActor sẽ được tạo động cho mỗi client connection
                // Chỉ đăng ký Props để có thể tạo sau này
                var clientActorProps = resolver.Props<ClientActor>();
                
                Console.WriteLine("DEBUG: ClientActor Props đã được đăng ký với Akka.Hosting");
            });
        }

        /// <summary>
        /// Đăng ký tất cả các Actor cần thiết cho game server
        /// </summary>
        public static AkkaConfigurationBuilder WithGameServerActors(this AkkaConfigurationBuilder builder)
        {
            Console.WriteLine("DEBUG: Bắt đầu WithGameServerActors...");

            var result = builder
                .WithActorRefProvider(Akka.Actor.ProviderSelection.Local.Instance);
            Console.WriteLine("DEBUG: Đã cấu hình ActorRefProvider");

            result = result.WithTcpManagerActor();
            Console.WriteLine("DEBUG: Đã gọi WithTcpManagerActor");

            result = result.WithPacketHandlerActor();
            Console.WriteLine("DEBUG: Đã gọi WithPacketHandlerActor");

            result = result.WithClientActor();
            Console.WriteLine("DEBUG: Đã gọi WithClientActor");

            Console.WriteLine("DEBUG: Hoàn thành WithGameServerActors");
            return result;
        }
    }
}
