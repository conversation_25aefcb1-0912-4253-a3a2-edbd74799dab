using System;
using System.Threading.Tasks;
using Akka.Actor;
using Akka.Hosting;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// Quản lý ActorSystem cho toàn bộ ứng dụng (chuẩn hóa cho Akka.Hosting)
    /// </summary>
    public class ActorSystemManager
    {
        private readonly ActorSystem _actorSystem;
        private readonly ActorRegistry _actorRegistry;

        public ActorSystemManager(ActorSystem actorSystem, ActorRegistry actorRegistry)
        {
            _actorSystem = actorSystem ?? throw new ArgumentNullException(nameof(actorSystem));
            _actorRegistry = actorRegistry ?? throw new ArgumentNullException(nameof(actorRegistry));

            Logger.Instance.Info("ActorSystemManager đã được khởi tạo thành công với Akka.Hosting");

            // Tạo và đăng ký TcpManagerActor ngay khi khởi tạo
            InitializeTcpManagerActor();
        }

        private void InitializeTcpManagerActor()
        {
            try
            {
                Console.WriteLine("DEBUG: Bắt đầu tạo TcpManagerActor trong ActorSystemManager...");

                // Tạo TcpManagerActor và đăng ký vào registry
                var tcpManagerProps = Props.Create<TcpManagerActor>();
                var tcpManager = _actorSystem.ActorOf(tcpManagerProps, "tcpManager");
                _actorRegistry.Register<TcpManagerActor>(tcpManager);

                Console.WriteLine("DEBUG: TcpManagerActor đã được tạo và đăng ký thành công");
                Console.WriteLine($"DEBUG: ActorRef: {tcpManager}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR: Lỗi khi tạo TcpManagerActor: {ex.Message}");
                Console.WriteLine($"ERROR: StackTrace: {ex.StackTrace}");
                throw;
            }
        }

        public ActorSystem ActorSystem => _actorSystem;

        public IActorRef TcpManagerActor
        {
            get
            {
                try
                {
                    return _actorRegistry.Get<TcpManagerActor>();
                }
                catch (Exception ex)
                {
                    Logger.Instance.Error($"Lỗi khi lấy TcpManagerActor từ registry: {ex.Message}");
                    throw;
                }
            }
        }

        /// <summary>
        /// Dừng ActorSystem
        /// </summary>
        public async Task ShutdownAsync()
        {
            try
            {
                if (_actorSystem != null)
                {
                    await _actorSystem.Terminate();
                    Logger.Instance.Info("ActorSystem đã được dừng thành công");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi dừng ActorSystem: {ex.Message}");
            }
        }
    }
}
