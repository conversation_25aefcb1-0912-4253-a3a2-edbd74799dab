using System;
using System.Threading.Tasks;
using Akka.Actor;
using Akka.Hosting;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// Quản lý ActorSystem cho toàn bộ ứng dụng (sử dụng Akka.Hosting cho AOT)
    /// </summary>
    public class ActorSystemManager
    {
        private readonly ActorSystem _actorSystem;
        private readonly ActorRegistry _actorRegistry;

        public ActorSystemManager(ActorSystem actorSystem, ActorRegistry actorRegistry)
        {
            _actorSystem = actorSystem ?? throw new ArgumentNullException(nameof(actorSystem));
            _actorRegistry = actorRegistry ?? throw new ArgumentNullException(nameof(actorRegistry));

            Console.WriteLine($"DEBUG: ActorSystem Name: {_actorSystem.Name}");
            Console.WriteLine($"DEBUG: ActorSystem WhenTerminated: {_actorSystem.WhenTerminated.IsCompleted}");
            Console.WriteLine($"DEBUG: ActorRegistry đã được khởi tạo");

            Logger.Instance.Info("ActorSystemManager đã được khởi tạo thành công với Akka.Hosting");
        }



        public ActorSystem ActorSystem => _actorSystem;

        public IActorRef TcpManagerActor
        {
            get
            {
                try
                {
                    Console.WriteLine($"DEBUG: Đang cố gắng lấy TcpManagerActor từ registry...");
                    Console.WriteLine($"DEBUG: ActorSystem.Name: {_actorSystem.Name}");
                    Console.WriteLine($"DEBUG: ActorSystem.IsTerminated: {_actorSystem.WhenTerminated.IsCompleted}");

                    // Thử lấy TcpManagerActor từ registry với retry logic
                    for (int i = 0; i < 10; i++)
                    {
                        try
                        {
                            var actor = _actorRegistry.Get<TcpManagerActor>();
                            Console.WriteLine($"DEBUG: Đã lấy được TcpManagerActor: {actor}");
                            return actor;
                        }
                        catch (Exception ex)
                        {
                            if (i == 9)
                            {
                                Console.WriteLine($"DEBUG: Lỗi cuối cùng khi lấy TcpManagerActor: {ex.Message}");
                                throw; // Ném lỗi ở lần thử cuối
                            }

                            Logger.Instance.Info($"TcpManagerActor chưa sẵn sàng, thử lại lần {i + 1}/10...");
                            System.Threading.Thread.Sleep(100); // Đợi 100ms
                        }
                    }

                    throw new InvalidOperationException("TcpManagerActor không thể được tạo sau 10 lần thử");
                }
                catch (Exception ex)
                {
                    Logger.Instance.Error($"Lỗi khi lấy TcpManagerActor từ registry: {ex.Message}");
                    throw;
                }
            }
        }

        /// <summary>
        /// Dừng ActorSystem
        /// </summary>
        public async Task ShutdownAsync()
        {
            try
            {
                if (_actorSystem != null)
                {
                    await _actorSystem.Terminate();
                    Logger.Instance.Info("ActorSystem đã được dừng thành công");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi dừng ActorSystem: {ex.Message}");
            }
        }
    }
}
